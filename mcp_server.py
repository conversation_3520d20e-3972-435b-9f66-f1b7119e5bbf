#!/usr/bin/env python3
"""
OCRFlux MCP Server with SSE Support
基于 SSE 协议的 MCP 服务器，集成 OCRFlux OCR 功能
"""

import asyncio
import json
import logging
import os
import tempfile
import traceback
from typing import Any, Dict, List, Optional, AsyncGenerator
from pathlib import Path

from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from sse_starlette import EventSourceResponse
import uvicorn

from mcp.server import Server
from mcp.types import (
    Tool,
    TextContent,
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    ListToolsResult,
)

from ocrflux.inference import RemoteVLLMClient, parse_async
from ocrflux.mcp_tools import parse_with_progress, stream_parse_events

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OCRFluxMCPServer:
    """OCRFlux MCP 服务器"""
    
    def __init__(self, vllm_url: str = "http://localhost:8011", model: str = "OCRFlux-3B"):
        """
        初始化 MCP 服务器
        
        Args:
            vllm_url: VLLM 服务的 URL
            model: 使用的模型名称
        """
        self.vllm_url = vllm_url
        self.model = model
        self.client = RemoteVLLMClient(vllm_url, model)
        self.server = Server("ocrflux-mcp")
        self.app = FastAPI(title="OCRFlux MCP Server", version="1.0.0")
        
        # 添加 CORS 中间件
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        self._setup_mcp_handlers()
        self._setup_fastapi_routes()
    
    def _setup_mcp_handlers(self):
        """设置 MCP 处理器"""
        
        @self.server.list_tools()
        async def list_tools() -> ListToolsResult:
            """列出可用的工具"""
            return ListToolsResult(
                tools=[
                    Tool(
                        name="ocr_parse_document",
                        description="解析文档（PDF或图片）并提取文本内容，支持跨页合并和表格识别",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "file_path": {
                                    "type": "string",
                                    "description": "文档文件路径"
                                },
                                "skip_cross_page_merge": {
                                    "type": "boolean",
                                    "description": "是否跳过跨页合并",
                                    "default": False
                                },
                                "max_page_retries": {
                                    "type": "integer",
                                    "description": "每页最大重试次数",
                                    "default": 1
                                }
                            },
                            "required": ["file_path"]
                        }
                    ),
                    Tool(
                        name="ocr_parse_document_stream",
                        description="流式解析文档，提供实时进度反馈",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "file_path": {
                                    "type": "string",
                                    "description": "文档文件路径"
                                },
                                "skip_cross_page_merge": {
                                    "type": "boolean",
                                    "description": "是否跳过跨页合并",
                                    "default": False
                                },
                                "max_page_retries": {
                                    "type": "integer",
                                    "description": "每页最大重试次数",
                                    "default": 1
                                }
                            },
                            "required": ["file_path"]
                        }
                    )
                ]
            )
        
        @self.server.call_tool()
        async def call_tool(name: str, arguments: Dict[str, Any]) -> CallToolResult:
            """调用工具"""
            try:
                if name == "ocr_parse_document":
                    return await self._handle_ocr_parse(arguments)
                elif name == "ocr_parse_document_stream":
                    return await self._handle_ocr_parse_stream(arguments)
                else:
                    raise ValueError(f"未知的工具: {name}")
            except Exception as e:
                logger.error(f"工具调用失败: {e}")
                logger.error(traceback.format_exc())
                return CallToolResult(
                    content=[TextContent(type="text", text=f"错误: {str(e)}")]
                )
    
    async def _handle_ocr_parse(self, arguments: Dict[str, Any]) -> CallToolResult:
        """处理 OCR 解析请求"""
        file_path = arguments.get("file_path")
        skip_cross_page_merge = arguments.get("skip_cross_page_merge", False)
        max_page_retries = arguments.get("max_page_retries", 1)
        
        if not file_path:
            raise ValueError("file_path 参数是必需的")
        
        if not os.path.exists(file_path):
            raise ValueError(f"文件不存在: {file_path}")
        
        logger.info(f"开始解析文档: {file_path}")
        
        # 调用 OCR 解析
        result = await parse_with_progress(
            self.vllm_url,
            self.model,
            file_path,
            skip_cross_page_merge=skip_cross_page_merge,
            max_page_retries=max_page_retries
        )
        
        if result is None:
            raise ValueError("文档解析失败")
        
        # 构建响应
        response_text = f"""文档解析完成！

文件路径: {result['orig_path']}
总页数: {result['num_pages']}
失败页面: {result.get('fallback_pages', [])}

解析结果:
{result['document_text']}
"""
        
        return CallToolResult(
            content=[TextContent(type="text", text=response_text)]
        )
    
    async def _handle_ocr_parse_stream(self, arguments: Dict[str, Any]) -> CallToolResult:
        """处理流式 OCR 解析请求"""
        # 对于 MCP 工具调用，我们返回一个包含流式 URL 的响应
        file_path = arguments.get("file_path")
        skip_cross_page_merge = arguments.get("skip_cross_page_merge", False)
        max_page_retries = arguments.get("max_page_retries", 1)
        
        if not file_path:
            raise ValueError("file_path 参数是必需的")
        
        if not os.path.exists(file_path):
            raise ValueError(f"文件不存在: {file_path}")
        
        # 生成一个任务 ID
        import uuid
        task_id = str(uuid.uuid4())
        
        # 存储任务参数（在实际应用中应该使用数据库或缓存）
        self._pending_tasks = getattr(self, '_pending_tasks', {})
        self._pending_tasks[task_id] = {
            'file_path': file_path,
            'skip_cross_page_merge': skip_cross_page_merge,
            'max_page_retries': max_page_retries
        }
        
        stream_url = f"http://localhost:8080/stream/{task_id}"
        
        return CallToolResult(
            content=[TextContent(
                type="text", 
                text=f"流式解析已启动。请访问以下 URL 获取实时进度:\n{stream_url}\n\n任务ID: {task_id}"
            )]
        )
    
    def _setup_fastapi_routes(self):
        """设置 FastAPI 路由"""
        
        @self.app.get("/")
        async def root():
            return {"message": "OCRFlux MCP Server", "version": "1.0.0"}
        
        @self.app.get("/health")
        async def health():
            return {"status": "healthy", "vllm_url": self.vllm_url, "model": self.model}
        
        @self.app.get("/stream/{task_id}")
        async def stream_ocr_parse(task_id: str):
            """流式 OCR 解析端点"""
            pending_tasks = getattr(self, '_pending_tasks', {})
            if task_id not in pending_tasks:
                raise HTTPException(status_code=404, detail="任务不存在")

            task_params = pending_tasks[task_id]

            async def generate_events():
                try:
                    async for event in self._stream_ocr_parse(**task_params):
                        yield event
                except Exception as e:
                    yield {
                        "event": "error",
                        "data": json.dumps({"error": str(e)})
                    }
                finally:
                    # 清理任务
                    pending_tasks.pop(task_id, None)

            return EventSourceResponse(generate_events())

        @self.app.post("/parse")
        async def parse_document(request: Request):
            """直接解析文档的 API 端点"""
            try:
                data = await request.json()
                file_path = data.get("file_path")
                skip_cross_page_merge = data.get("skip_cross_page_merge", False)
                max_page_retries = data.get("max_page_retries", 1)

                if not file_path:
                    raise HTTPException(status_code=400, detail="file_path 参数是必需的")

                if not os.path.exists(file_path):
                    raise HTTPException(status_code=404, detail=f"文件不存在: {file_path}")

                result = await parse_with_progress(
                    self.vllm_url,
                    self.model,
                    file_path,
                    skip_cross_page_merge=skip_cross_page_merge,
                    max_page_retries=max_page_retries
                )

                if result is None:
                    raise HTTPException(status_code=500, detail="文档解析失败")

                return {"success": True, "result": result}

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"解析失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/parse/stream")
        async def parse_document_stream(request: Request):
            """创建流式解析任务"""
            try:
                data = await request.json()
                file_path = data.get("file_path")
                skip_cross_page_merge = data.get("skip_cross_page_merge", False)
                max_page_retries = data.get("max_page_retries", 1)

                if not file_path:
                    raise HTTPException(status_code=400, detail="file_path 参数是必需的")

                if not os.path.exists(file_path):
                    raise HTTPException(status_code=404, detail=f"文件不存在: {file_path}")

                # 生成任务 ID
                import uuid
                task_id = str(uuid.uuid4())

                # 存储任务参数
                self._pending_tasks = getattr(self, '_pending_tasks', {})
                self._pending_tasks[task_id] = {
                    'file_path': file_path,
                    'skip_cross_page_merge': skip_cross_page_merge,
                    'max_page_retries': max_page_retries
                }

                return {
                    "success": True,
                    "task_id": task_id,
                    "stream_url": f"/stream/{task_id}"
                }

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"创建流式任务失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
    
    async def _stream_ocr_parse(self, file_path: str, skip_cross_page_merge: bool = False, max_page_retries: int = 1) -> AsyncGenerator[Dict[str, str], None]:
        """流式 OCR 解析生成器"""
        try:
            async for event in stream_parse_events(
                self.vllm_url,
                self.model,
                file_path,
                skip_cross_page_merge=skip_cross_page_merge,
                max_page_retries=max_page_retries
            ):
                # 转换事件格式为 SSE 格式
                yield {
                    "event": event["event"],
                    "data": json.dumps(event["data"])
                }

        except Exception as e:
            logger.error(f"流式解析失败: {e}")
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }
    
    async def run_mcp_server(self, transport_type: str = "stdio"):
        """运行 MCP 服务器"""
        if transport_type == "stdio":
            from mcp.server.stdio import stdio_server
            async with stdio_server() as (read_stream, write_stream):
                await self.server.run(read_stream, write_stream, self.server.create_initialization_options())
        else:
            raise ValueError(f"不支持的传输类型: {transport_type}")
    
    def run_fastapi_server(self, host: str = "0.0.0.0", port: int = 8080):
        """运行 FastAPI 服务器"""
        uvicorn.run(self.app, host=host, port=port)

# 全局服务器实例
server_instance = None

def create_server(vllm_url: str = None, model: str = None) -> OCRFluxMCPServer:
    """创建服务器实例"""
    global server_instance
    
    # 从环境变量获取配置
    vllm_url = vllm_url or os.getenv("VLLM_URL", "http://localhost:8000")
    model = model or os.getenv("MODEL_NAME", "OCRFlux-3B")
    
    server_instance = OCRFluxMCPServer(vllm_url, model)
    return server_instance

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "fastapi":
        # 运行 FastAPI 服务器
        server = create_server()
        server.run_fastapi_server()
    else:
        # 运行 MCP 服务器
        server = create_server()
        asyncio.run(server.run_mcp_server())
