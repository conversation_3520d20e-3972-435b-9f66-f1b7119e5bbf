#!/usr/bin/env python3
"""
OCRFlux FastMCP 文件上传测试客户端
测试文件上传功能和 Base64 编码
"""

import asyncio
import base64
import json
import sys
import aiohttp
from pathlib import Path

async def encode_file_to_base64(file_path: str) -> str:
    """将文件编码为 Base64 字符串"""
    try:
        with open(file_path, 'rb') as f:
            file_data = f.read()
        
        base64_data = base64.b64encode(file_data).decode('utf-8')
        print(f"文件 {file_path} 编码为 Base64，大小: {len(base64_data)} 字符")
        return base64_data
        
    except Exception as e:
        print(f"编码文件失败: {e}")
        raise

async def test_file_upload_parse(server_url: str, file_path: str):
    """测试文件上传解析功能"""
    
    print(f"测试文件上传解析: {file_path}")
    print(f"服务器: {server_url}")
    print("-" * 50)
    
    # 编码文件
    try:
        file_content = await encode_file_to_base64(file_path)
        filename = Path(file_path).name
    except Exception as e:
        print(f"❌ 文件编码失败: {e}")
        return
    
    async with aiohttp.ClientSession() as session:
        # 测试文件上传解析
        print("1. 测试文件上传解析...")
        
        try:
            async with session.post(
                f"{server_url}/call_tool",
                json={
                    "name": "ocr_parse_document",
                    "arguments": {
                        "file_content": file_content,
                        "filename": filename,
                        "skip_cross_page_merge": False,
                        "max_page_retries": 2
                    }
                }
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    if "error" in result:
                        print(f"❌ 解析失败: {result['error']}")
                    else:
                        print(f"✅ 解析成功!")
                        print(f"   消息: {result.get('message', '')}")
                        
                        # 显示解析结果摘要
                        parse_result = result.get('result', {})
                        if parse_result:
                            print(f"   文件: {parse_result.get('orig_path', '')}")
                            print(f"   页数: {parse_result.get('num_pages', 0)}")
                            print(f"   失败页面: {parse_result.get('fallback_pages', [])}")
                            
                            # 显示部分文本内容
                            document_text = parse_result.get('document_text', '')
                            if document_text:
                                preview = document_text[:200] + "..." if len(document_text) > 200 else document_text
                                print(f"   内容预览: {preview}")
                else:
                    error_text = await resp.text()
                    print(f"❌ 请求失败: {resp.status} - {error_text}")
        except Exception as e:
            print(f"❌ 解析请求失败: {e}")

async def test_file_upload_stream(server_url: str, file_path: str):
    """测试文件上传流式解析功能"""
    
    print(f"\n测试文件上传流式解析: {file_path}")
    print("-" * 50)
    
    # 编码文件
    try:
        file_content = await encode_file_to_base64(file_path)
        filename = Path(file_path).name
    except Exception as e:
        print(f"❌ 文件编码失败: {e}")
        return
    
    async with aiohttp.ClientSession() as session:
        # 创建流式任务
        print("1. 创建流式解析任务...")
        
        try:
            async with session.post(
                f"{server_url}/call_tool",
                json={
                    "name": "ocr_parse_document_stream",
                    "arguments": {
                        "file_content": file_content,
                        "filename": filename,
                        "skip_cross_page_merge": False,
                        "max_page_retries": 2
                    }
                }
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    if "error" in result:
                        print(f"❌ 创建流式任务失败: {result['error']}")
                        return
                    else:
                        print(f"✅ 流式任务创建成功!")
                        print(f"   消息: {result.get('message', '')}")
                        task_id = result.get('task_id', '')
                        stream_url = result.get('stream_url', '')
                        print(f"   任务ID: {task_id}")
                        print(f"   流URL: {stream_url}")
                        
                        # 测试清理任务
                        if task_id:
                            await test_cleanup_task(session, server_url, task_id)
                else:
                    error_text = await resp.text()
                    print(f"❌ 创建流式任务失败: {resp.status} - {error_text}")
        except Exception as e:
            print(f"❌ 流式任务请求失败: {e}")

async def test_cleanup_task(session: aiohttp.ClientSession, server_url: str, task_id: str):
    """测试清理任务功能"""
    
    print(f"\n2. 测试清理任务: {task_id}")
    
    try:
        async with session.post(
            f"{server_url}/call_tool",
            json={
                "name": "cleanup_stream_task",
                "arguments": {
                    "task_id": task_id
                }
            }
        ) as resp:
            if resp.status == 200:
                result = await resp.json()
                if "error" in result:
                    print(f"❌ 清理任务失败: {result['error']}")
                else:
                    print(f"✅ 任务清理成功: {result.get('message', '')}")
            else:
                error_text = await resp.text()
                print(f"❌ 清理任务请求失败: {resp.status} - {error_text}")
    except Exception as e:
        print(f"❌ 清理任务请求失败: {e}")

async def test_server_status(server_url: str):
    """测试服务器状态"""
    
    print("测试服务器状态...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                f"{server_url}/call_tool",
                json={
                    "name": "get_server_status",
                    "arguments": {}
                }
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"✅ 服务器状态:")
                    for key, value in result.items():
                        print(f"   {key}: {value}")
                else:
                    error_text = await resp.text()
                    print(f"❌ 获取服务器状态失败: {resp.status} - {error_text}")
        except Exception as e:
            print(f"❌ 服务器状态请求失败: {e}")

async def create_test_file():
    """创建测试文件"""
    test_content = """这是一个测试文档，用于测试 OCRFlux FastMCP 服务器的文件上传功能。

文档内容包括：
1. 中文文本
2. 英文文本 (English text)
3. 数字 123456
4. 特殊符号 @#$%^&*()

测试目的：
- 验证文件上传和 Base64 编码功能
- 测试 MCP 工具的文件处理能力
- 检查临时文件的创建和清理
- 确认错误处理机制

This is a test document for OCRFlux FastMCP server file upload functionality testing.
"""
    
    test_file = "test_upload.txt"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print(f"创建测试文件: {test_file}")
    return test_file

async def main():
    """主函数"""
    
    if len(sys.argv) < 2:
        print("用法: python test_file_upload_client.py <文件路径> [服务器URL]")
        print("示例: python test_file_upload_client.py test.pdf http://localhost:8080")
        print("或者: python test_file_upload_client.py --create-test [服务器URL]")
        sys.exit(1)
    
    if sys.argv[1] == "--create-test":
        file_path = await create_test_file()
        server_url = sys.argv[2] if len(sys.argv) > 2 else "http://localhost:8080"
    else:
        file_path = sys.argv[1]
        server_url = sys.argv[2] if len(sys.argv) > 2 else "http://localhost:8080"
    
    # 检查文件是否存在
    if not Path(file_path).exists():
        print(f"错误: 文件不存在 - {file_path}")
        sys.exit(1)
    
    print("OCRFlux FastMCP 文件上传测试客户端")
    print("=" * 50)
    
    # 测试服务器状态
    await test_server_status(server_url)
    
    print("\n" + "=" * 50)
    
    # 测试文件上传解析
    await test_file_upload_parse(server_url, file_path)
    
    print("\n" + "=" * 50)
    
    # 测试文件上传流式解析
    await test_file_upload_stream(server_url, file_path)
    
    print("\n" + "=" * 50)
    print("测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
