# OCRFlux MCP 服务器项目概览

## 项目简介

本项目基于 OCRFlux 的 `inference.py` 业务代码，构建了一个支持 SSE (Server-Sent Events) 协议的 MCP (Model Context Protocol) 服务器。该服务器提供了实时进度反馈的文档 OCR 解析功能。

## 核心功能

### 🔧 MCP 工具集成
- 将 OCRFlux OCR 功能封装为标准 MCP 工具
- 支持文档解析和流式解析两种模式
- 完全兼容 MCP 协议规范

### 📡 SSE 流式响应
- 实时进度反馈
- 事件驱动的状态更新
- 支持长时间运行的 OCR 任务

### 🌐 HTTP API
- RESTful API 接口
- 支持直接文档解析
- 流式任务创建和管理

### ⚙️ 灵活配置
- YAML 配置文件支持
- 环境变量配置
- 命令行参数覆盖

## 项目结构

```
OCRFlux-main/
├── mcp_server.py              # MCP 服务器主文件
├── start_mcp_server.py        # 启动脚本
├── config.yaml                # 配置文件
├── mcp_client_config.json     # MCP 客户端配置
├── ocrflux/
│   ├── mcp_tools.py          # MCP 工具模块 (新增)
│   ├── inference.py          # OCR 推理模块 (原有)
│   └── ...                   # 其他原有模块
├── start_server.bat          # Windows 启动脚本
├── start_server.sh           # Linux/macOS 启动脚本
├── test_sse_client.py        # SSE 客户端测试
├── demo.html                 # Web 演示页面
├── MCP_SERVER_README.md      # 详细使用文档
└── PROJECT_OVERVIEW.md       # 项目概览 (本文件)
```

## 新增文件说明

### 核心文件

1. **`mcp_server.py`** - MCP 服务器主文件
   - 实现 MCP 协议处理
   - 集成 FastAPI HTTP 服务器
   - 提供 SSE 流式响应

2. **`ocrflux/mcp_tools.py`** - MCP 工具模块
   - 带进度跟踪的 OCR 解析函数
   - 流式事件生成器
   - 进度回调机制

3. **`start_mcp_server.py`** - 启动脚本
   - 配置文件加载
   - 多种运行模式支持
   - 命令行参数处理

### 配置文件

4. **`config.yaml`** - 主配置文件
   - VLLM 服务配置
   - 服务器参数设置
   - OCR 解析选项

5. **`mcp_client_config.json`** - MCP 客户端配置
   - 标准 MCP 客户端配置格式
   - 环境变量设置

### 启动脚本

6. **`start_server.bat`** / **`start_server.sh`** - 平台启动脚本
   - 自动环境检查
   - 依赖安装
   - 交互式模式选择

### 测试和演示

7. **`test_sse_client.py`** - SSE 客户端测试
   - 完整的 SSE 流测试
   - 直接 API 测试
   - 服务器健康检查

8. **`demo.html`** - Web 演示页面
   - 可视化 SSE 进度显示
   - 实时日志查看
   - 结果展示

### 文档

9. **`MCP_SERVER_README.md`** - 详细使用文档
   - 安装和配置指南
   - API 参考
   - 故障排除

## 技术特性

### MCP 协议支持
- 标准 MCP 工具定义
- stdio 传输模式
- 完整的错误处理

### SSE 流式技术
- 实时进度更新
- 事件类型分类 (start, progress, complete, error)
- 自动连接管理

### 异步处理
- 基于 asyncio 的异步架构
- 并发任务处理
- 非阻塞 I/O 操作

### 错误处理
- 完善的异常捕获
- 详细的错误信息
- 优雅的失败恢复

## 使用场景

### 1. MCP 客户端集成
```bash
# 作为 MCP 工具在支持 MCP 的应用中使用
python start_mcp_server.py --mode mcp
```

### 2. HTTP API 服务
```bash
# 作为独立的 HTTP API 服务器
python start_mcp_server.py --mode fastapi
```

### 3. 混合模式
```bash
# 同时提供 MCP 和 HTTP API 服务
python start_mcp_server.py --mode both
```

## 依赖要求

### 新增依赖
- `mcp>=1.0.0` - MCP 协议支持
- `fastapi>=0.104.0` - HTTP API 框架
- `uvicorn>=0.24.0` - ASGI 服务器
- `sse-starlette>=1.6.5` - SSE 支持
- `pyyaml>=6.0` - YAML 配置解析

### 原有依赖
- `beautifulsoup4>=4.13.4`
- `lxml>=6.0.0`
- `pillow>=11.3.0`
- `pymupdf>=1.26.3`
- `tqdm>=4.67.1`

## 快速开始

1. **安装依赖**
   ```bash
   pip install -e .
   ```

2. **配置 VLLM 服务**
   ```bash
   # 确保 VLLM 服务在运行
   # 默认: http://localhost:8000
   ```

3. **启动服务器**
   ```bash
   # Windows
   start_server.bat
   
   # Linux/macOS
   ./start_server.sh
   ```

4. **测试功能**
   ```bash
   # 测试 SSE 流式解析
   python test_sse_client.py document.pdf
   
   # 或打开 demo.html 进行 Web 测试
   ```

## 扩展性

### 添加新工具
1. 在 `OCRFluxMCPServer._setup_mcp_handlers()` 中定义新工具
2. 实现对应的处理函数
3. 更新文档

### 自定义进度跟踪
1. 扩展 `ProgressTracker` 类
2. 实现自定义回调函数
3. 集成到解析流程中

### 配置扩展
1. 更新 `config.yaml` 模板
2. 修改配置加载逻辑
3. 添加验证规则

## 总结

本项目成功将 OCRFlux 的核心 OCR 功能包装为现代化的 MCP 服务，同时提供了丰富的 SSE 流式响应功能。通过灵活的配置和多种运行模式，可以满足不同场景下的使用需求。

项目保持了与原有 `inference.py` 代码的完全兼容性，同时添加了现代化的服务架构和实时反馈机制，为 OCR 处理提供了更好的用户体验。
