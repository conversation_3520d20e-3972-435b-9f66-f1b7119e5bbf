# OCRFlux FastMCP 服务器指南

## 概述

基于 FastMCP 框架重新构建的 OCRFlux MCP 服务器，提供更现代化、更易维护的架构。

## 架构特点

### 1. FastMCP 框架
- 使用 `@mcp.tool()` 装饰器定义工具
- 自动处理 MCP 协议细节
- 支持 SSE 和 stdio 传输协议
- 内置错误处理和类型安全

### 2. 配置管理
- 基于 Pydantic BaseModel 的配置类
- 支持环境变量和命令行参数
- 分层配置系统（环境变量 → 配置文件 → 命令行参数）

### 3. 类型安全
- 使用 TypedDict 定义响应类型
- 完整的类型提示支持
- 结构化错误响应

## 文件结构

```
├── ocrflux_mcp_server.py      # 新的 FastMCP 服务器
├── start_fastmcp_server.py    # FastMCP 服务器启动脚本
├── test_fastmcp_client.py     # FastMCP 客户端测试
├── mcp_server.py              # 传统 MCP 服务器（向后兼容）
├── start_mcp_server.py        # 传统服务器启动脚本
└── mcp_client_config.json     # MCP 客户端配置
```

## 快速开始

### 1. 安装依赖

```bash
pip install -e .
```

### 2. 启动服务器

**推荐：使用 FastMCP (SSE 传输)**
```bash
python start_fastmcp_server.py --transport sse
```

**或使用 stdio 传输**
```bash
python start_fastmcp_server.py --transport stdio
```

### 3. 测试服务器

**基本功能测试**
```bash
# 测试 FastMCP 服务器
python test_fastmcp_client.py test.pdf

# 指定服务器地址
python test_fastmcp_client.py test.pdf http://localhost:8080
```

**文件上传功能测试**
```bash
# 测试文件上传功能
python test_file_upload_client.py test.pdf

# 创建测试文件并测试
python test_file_upload_client.py --create-test

# 指定服务器地址
python test_file_upload_client.py test.pdf http://localhost:8080
```

## 配置选项

### 环境变量

```bash
export VLLM_URL="http://localhost:8000"
export MODEL_NAME="OCRFlux-3B"
export MAX_PAGE_RETRIES="3"
export SKIP_CROSS_PAGE_MERGE="false"
export MCP_SERVER_HOST="localhost"
export MCP_SERVER_PORT="8080"
```

### 命令行参数

```bash
python start_fastmcp_server.py \
  --transport sse \
  --vllm-url http://localhost:8000 \
  --model OCRFlux-3B \
  --max-page-retries 3 \
  --host localhost \
  --port 8080
```

### 配置文件 (config.yaml)

```yaml
vllm:
  url: "http://localhost:8000"
  model: "OCRFlux-3B"

ocr:
  max_page_retries: 3
  skip_cross_page_merge: false

mcp:
  host: "localhost"
  port: 8080

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
```

## MCP 工具

### 1. ocr_parse_document

直接解析文档并返回完整结果。支持本地文件路径或上传文件内容。

**参数:**
- `file_path` (string, 可选): 本地文档文件路径（与 file_content 二选一）
- `file_content` (string, 可选): Base64 编码的文件内容（与 file_path 二选一）
- `filename` (string, 可选): 原始文件名（使用 file_content 时建议提供）
- `skip_cross_page_merge` (boolean, 可选): 跳过跨页合并
- `max_page_retries` (integer, 可选): 每页最大重试次数

**使用方式:**
```json
// 方式1: 本地文件
{
  "name": "ocr_parse_document",
  "arguments": {
    "file_path": "/path/to/document.pdf",
    "max_page_retries": 3
  }
}

// 方式2: 上传文件
{
  "name": "ocr_parse_document",
  "arguments": {
    "file_content": "base64_encoded_file_content...",
    "filename": "document.pdf",
    "max_page_retries": 3
  }
}
```

**返回:**
```typescript
OCRParseResponse | ErrorResponse
```

### 2. ocr_parse_document_stream

创建流式解析任务，支持进度跟踪。支持本地文件路径或上传文件内容。

**参数:**
- `file_path` (string, 可选): 本地文档文件路径（与 file_content 二选一）
- `file_content` (string, 可选): Base64 编码的文件内容（与 file_path 二选一）
- `filename` (string, 可选): 原始文件名（使用 file_content 时建议提供）
- `skip_cross_page_merge` (boolean, 可选): 跳过跨页合并
- `max_page_retries` (integer, 可选): 每页最大重试次数

**返回:**
```typescript
StreamTaskResponse | ErrorResponse
```

### 3. cleanup_stream_task

清理流式解析任务及其临时文件。

**参数:**
- `task_id` (string): 任务 ID

**返回:**
```typescript
SuccessResponse | ErrorResponse
```

### 4. get_server_status

获取服务器状态信息。

**返回:**
```typescript
{
  status: string;
  vllm_url: string;
  model: string;
  max_page_retries: number;
  skip_cross_page_merge: boolean;
  active_tasks: number;
  temp_files: number;
  timestamp: string;
}
```

## 响应类型

### SuccessResponse
```typescript
{
  message: string;
}
```

### ErrorResponse
```typescript
{
  error: string;
}
```

### OCRParseResponse
```typescript
{
  message: string;
  result: {
    orig_path: string;
    num_pages: number;
    document_text: string;
    fallback_pages: number[];
    // ... 其他字段
  };
}
```

### StreamTaskResponse
```typescript
{
  message: string;
  task_id: string;
  stream_url: string;
}
```

## 与传统服务器的区别

| 特性 | FastMCP 服务器 | 传统服务器 |
|------|----------------|------------|
| 框架 | FastMCP | 手动 MCP 协议 |
| 工具定义 | `@mcp.tool()` 装饰器 | 手动注册 |
| 配置管理 | Pydantic BaseModel | 字典配置 |
| 类型安全 | TypedDict + 类型提示 | 基本类型 |
| 错误处理 | 结构化响应 | 异常处理 |
| 传输协议 | SSE + stdio | FastAPI + MCP |
| 维护性 | 高 | 中等 |

## 迁移指南

如果你正在使用传统的 MCP 服务器，可以按以下步骤迁移到 FastMCP：

1. **更新客户端配置**
   ```json
   {
     "mcpServers": {
       "ocrflux": {
         "command": "python",
         "args": ["start_fastmcp_server.py", "--transport", "stdio"]
       }
     }
   }
   ```

2. **更新环境变量**
   - 保持现有的环境变量不变
   - 可选：添加新的配置选项

3. **测试功能**
   ```bash
   python test_fastmcp_client.py test.pdf
   ```

## 故障排除

### 1. 服务器无法启动
- 检查 VLLM 服务是否运行
- 验证端口是否被占用
- 查看日志输出

### 2. 工具调用失败
- 确认文件路径正确
- 检查 VLLM 服务连接
- 验证模型名称

### 3. 流式任务无响应
- 检查任务 ID 是否有效
- 验证 SSE 连接
- 查看服务器日志

## 开发指南

### 添加新工具

```python
@mcp.tool()
async def new_tool(param: str) -> SuccessResponse | ErrorResponse:
    """新工具描述"""
    try:
        # 工具逻辑
        return SuccessResponse(message="成功")
    except Exception as e:
        return ErrorResponse(error=str(e))
```

### 扩展配置

```python
class NewConfig(BaseModel):
    new_param: str = "default_value"
    
    @classmethod
    def from_env(cls) -> 'NewConfig':
        return cls(
            new_param=os.environ.get('NEW_PARAM', 'default_value')
        )
```

### 自定义响应类型

```python
class CustomResponse(TypedDict):
    custom_field: str
    data: Dict[str, Any]
```

## 性能优化

1. **并发处理**: FastMCP 支持异步处理多个请求
2. **连接池**: 复用 VLLM 客户端连接
3. **缓存**: 可添加结果缓存机制
4. **流式处理**: 使用 SSE 提供实时反馈

## 安全考虑

1. **输入验证**: 使用 Pydantic 验证输入参数
2. **路径安全**: 验证文件路径防止目录遍历
3. **错误处理**: 避免泄露敏感信息
4. **访问控制**: 可添加认证和授权机制
