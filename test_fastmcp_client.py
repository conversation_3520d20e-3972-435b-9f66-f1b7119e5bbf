#!/usr/bin/env python3
"""
OCRFlux FastMCP 客户端测试
测试基于 FastMCP 框架的 OCRFlux MCP 服务器
"""

import asyncio
import json
import sys
import aiohttp
from pathlib import Path

async def test_mcp_tools(server_url: str, file_path: str):
    """测试 MCP 工具"""
    
    print(f"测试文件: {file_path}")
    print(f"服务器: {server_url}")
    print("-" * 50)
    
    async with aiohttp.ClientSession() as session:
        # 1. 测试服务器状态
        print("1. 检查服务器状态...")
        
        try:
            async with session.post(
                f"{server_url}/call_tool",
                json={
                    "name": "get_server_status",
                    "arguments": {}
                }
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"✅ 服务器状态: {result}")
                else:
                    print(f"❌ 获取服务器状态失败: {resp.status}")
                    return
        except Exception as e:
            print(f"❌ 连接服务器失败: {e}")
            return
        
        # 2. 测试直接文档解析
        print("\n2. 测试直接文档解析...")
        
        try:
            async with session.post(
                f"{server_url}/call_tool",
                json={
                    "name": "ocr_parse_document",
                    "arguments": {
                        "file_path": file_path,
                        "skip_cross_page_merge": False,
                        "max_page_retries": 2
                    }
                }
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    if "error" in result:
                        print(f"❌ 解析失败: {result['error']}")
                    else:
                        print(f"✅ 解析成功!")
                        print(f"   消息: {result.get('message', '')}")
                        
                        # 显示解析结果摘要
                        parse_result = result.get('result', {})
                        if parse_result:
                            print(f"   文件: {parse_result.get('orig_path', '')}")
                            print(f"   页数: {parse_result.get('num_pages', 0)}")
                            print(f"   失败页面: {parse_result.get('fallback_pages', [])}")
                            
                            # 显示部分文本内容
                            document_text = parse_result.get('document_text', '')
                            if document_text:
                                preview = document_text[:200] + "..." if len(document_text) > 200 else document_text
                                print(f"   内容预览: {preview}")
                else:
                    error_text = await resp.text()
                    print(f"❌ 请求失败: {resp.status} - {error_text}")
        except Exception as e:
            print(f"❌ 解析请求失败: {e}")
        
        # 3. 测试流式解析任务创建
        print("\n3. 测试流式解析任务创建...")
        
        try:
            async with session.post(
                f"{server_url}/call_tool",
                json={
                    "name": "ocr_parse_document_stream",
                    "arguments": {
                        "file_path": file_path,
                        "skip_cross_page_merge": False,
                        "max_page_retries": 2
                    }
                }
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    if "error" in result:
                        print(f"❌ 创建流式任务失败: {result['error']}")
                    else:
                        print(f"✅ 流式任务创建成功!")
                        print(f"   消息: {result.get('message', '')}")
                        print(f"   任务ID: {result.get('task_id', '')}")
                        print(f"   流URL: {result.get('stream_url', '')}")
                else:
                    error_text = await resp.text()
                    print(f"❌ 创建流式任务失败: {resp.status} - {error_text}")
        except Exception as e:
            print(f"❌ 流式任务请求失败: {e}")

async def test_mcp_list_tools(server_url: str):
    """测试 MCP 工具列表"""
    
    print("测试 MCP 工具列表...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                f"{server_url}/list_tools",
                json={}
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    tools = result.get('tools', [])
                    print(f"✅ 发现 {len(tools)} 个工具:")
                    
                    for tool in tools:
                        print(f"   - {tool.get('name', '')}: {tool.get('description', '')}")
                        
                        # 显示参数信息
                        input_schema = tool.get('inputSchema', {})
                        properties = input_schema.get('properties', {})
                        if properties:
                            print(f"     参数:")
                            for param_name, param_info in properties.items():
                                param_type = param_info.get('type', 'unknown')
                                param_desc = param_info.get('description', '')
                                required = param_name in input_schema.get('required', [])
                                req_str = " (必需)" if required else " (可选)"
                                print(f"       {param_name} ({param_type}){req_str}: {param_desc}")
                else:
                    error_text = await resp.text()
                    print(f"❌ 获取工具列表失败: {resp.status} - {error_text}")
        except Exception as e:
            print(f"❌ 工具列表请求失败: {e}")

async def check_server_health(server_url: str):
    """检查服务器健康状态"""
    
    print(f"检查服务器健康状态...")
    
    async with aiohttp.ClientSession() as session:
        try:
            # 尝试连接服务器根路径
            async with session.get(server_url) as resp:
                if resp.status == 200:
                    print(f"✅ 服务器响应正常")
                    return True
                else:
                    print(f"❌ 服务器响应异常: {resp.status}")
                    return False
        except Exception as e:
            print(f"❌ 无法连接到服务器: {e}")
            return False

async def main():
    """主函数"""
    
    if len(sys.argv) < 2:
        print("用法: python test_fastmcp_client.py <文件路径> [服务器URL]")
        print("示例: python test_fastmcp_client.py test.pdf http://localhost:8080")
        sys.exit(1)
    
    file_path = sys.argv[1]
    server_url = sys.argv[2] if len(sys.argv) > 2 else "http://localhost:8080"
    
    # 检查文件是否存在
    if not Path(file_path).exists():
        print(f"错误: 文件不存在 - {file_path}")
        sys.exit(1)
    
    print("OCRFlux FastMCP 客户端测试")
    print("=" * 50)
    
    # 检查服务器健康状态
    if not await check_server_health(server_url):
        print("请确保 FastMCP 服务器正在运行并且可访问")
        print("启动命令: python start_fastmcp_server.py --transport sse")
        sys.exit(1)
    
    print("\n选择测试模式:")
    print("1. 测试 MCP 工具功能")
    print("2. 列出可用工具")
    print("3. 完整测试")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
    except KeyboardInterrupt:
        print("\n测试取消")
        sys.exit(0)
    
    try:
        if choice == "1":
            await test_mcp_tools(server_url, file_path)
        elif choice == "2":
            await test_mcp_list_tools(server_url)
        elif choice == "3":
            await test_mcp_list_tools(server_url)
            print("\n" + "=" * 50)
            await test_mcp_tools(server_url, file_path)
        else:
            print("无效选择，执行完整测试")
            await test_mcp_list_tools(server_url)
            print("\n" + "=" * 50)
            await test_mcp_tools(server_url, file_path)
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
