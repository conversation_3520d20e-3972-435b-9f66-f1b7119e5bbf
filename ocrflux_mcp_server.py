#!/usr/bin/env python3
"""
OCRFlux MCP 服务器 - 基于 FastMCP 框架
"""

import argparse
import asyncio
import base64
import json
import logging
import os
import sys
import tempfile
import uuid
import yaml
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, TypedDict, cast, Union
from pathlib import Path

from dotenv import load_dotenv
from mcp.server.fastmcp import FastMCP
from pydantic import BaseModel, Field

from ocrflux.mcp_tools import parse_with_progress, stream_parse_events

# 加载环境变量
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), ".env"))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream=sys.stderr,
)
logger = logging.getLogger(__name__)


def load_config_file(config_path: str = "config.yaml") -> Dict[str, Any]:
    """加载 YAML 配置文件

    Args:
        config_path: 配置文件路径

    Returns:
        配置字典
    """
    try:
        if not os.path.exists(config_path):
            logger.warning(f"配置文件不存在: {config_path}，使用默认配置")
            return {}

        with open(config_path, "r", encoding="utf-8") as f:
            config_data = yaml.safe_load(f) or {}

        logger.info(f"成功加载配置文件: {config_path}")
        return config_data

    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return {}


# 默认配置
DEFAULT_VLLM_URL = "http://localhost:8000"
DEFAULT_MODEL = "OCRFlux-3B"


# 类型定义
class ErrorResponse(TypedDict):
    error: str


class SuccessResponse(TypedDict):
    message: str


class OCRParseResponse(TypedDict):
    message: str
    result: Dict[str, Any]


class StreamTaskResponse(TypedDict):
    message: str
    task_id: str
    stream_url: str


# 配置类
class OCRFluxConfig(BaseModel):
    """OCRFlux 服务器配置"""

    vllm_url: str = DEFAULT_VLLM_URL
    model: str = DEFAULT_MODEL
    max_page_retries: int = 3
    skip_cross_page_merge: bool = False

    @classmethod
    def from_env(cls) -> "OCRFluxConfig":
        """从环境变量创建配置"""
        return cls(
            vllm_url=os.environ.get("VLLM_URL", DEFAULT_VLLM_URL),
            model=os.environ.get("MODEL_NAME", DEFAULT_MODEL),
            max_page_retries=int(os.environ.get("MAX_PAGE_RETRIES", "3")),
            skip_cross_page_merge=os.environ.get(
                "SKIP_CROSS_PAGE_MERGE", "false"
            ).lower()
            == "true",
        )

    @classmethod
    def from_config_file_and_cli(
        cls, config_data: Dict[str, Any], args: argparse.Namespace
    ) -> "OCRFluxConfig":
        """从配置文件、命令行参数和环境变量创建配置

        优先级：命令行参数 > 环境变量 > 配置文件 > 默认值
        """
        # 从配置文件获取默认值
        vllm_config = config_data.get("vllm", {})
        ocr_config = config_data.get("ocr", {})

        # 构建配置，按优先级覆盖
        vllm_url = DEFAULT_VLLM_URL
        if vllm_config.get("url"):
            vllm_url = vllm_config["url"]
        if os.environ.get("VLLM_URL"):
            vllm_url = os.environ["VLLM_URL"]
        if hasattr(args, "vllm_url") and args.vllm_url:
            vllm_url = args.vllm_url

        model = DEFAULT_MODEL
        if vllm_config.get("model"):
            model = vllm_config["model"]
        if os.environ.get("MODEL_NAME"):
            model = os.environ["MODEL_NAME"]
        if hasattr(args, "model") and args.model:
            model = args.model

        max_page_retries = 3
        if ocr_config.get("max_page_retries") is not None:
            max_page_retries = ocr_config["max_page_retries"]
        if os.environ.get("MAX_PAGE_RETRIES"):
            max_page_retries = int(os.environ["MAX_PAGE_RETRIES"])
        if hasattr(args, "max_page_retries") and args.max_page_retries is not None:
            max_page_retries = args.max_page_retries

        skip_cross_page_merge = False
        if ocr_config.get("skip_cross_page_merge") is not None:
            skip_cross_page_merge = ocr_config["skip_cross_page_merge"]
        if os.environ.get("SKIP_CROSS_PAGE_MERGE"):
            skip_cross_page_merge = (
                os.environ["SKIP_CROSS_PAGE_MERGE"].lower() == "true"
            )
        if (
            hasattr(args, "skip_cross_page_merge")
            and args.skip_cross_page_merge is not None
        ):
            skip_cross_page_merge = args.skip_cross_page_merge

        return cls(
            vllm_url=vllm_url,
            model=model,
            max_page_retries=max_page_retries,
            skip_cross_page_merge=skip_cross_page_merge,
        )


class MCPConfig(BaseModel):
    """MCP 服务器配置"""

    transport: str = "sse"  # 默认使用 SSE 传输

    @classmethod
    def from_cli(cls, args: argparse.Namespace) -> "MCPConfig":
        """从命令行参数创建 MCP 配置"""
        return cls(transport=args.transport)


# 全局配置实例
config = OCRFluxConfig()

# MCP 服务器说明
OCRFLUX_MCP_INSTRUCTIONS = """
OCRFlux MCP 服务器提供强大的文档 OCR (光学字符识别) 功能，支持多种文档格式的文本提取。

主要功能：
1. 文档解析 - 使用 ocr_parse_document 工具解析文档并提取文本
2. 流式解析 - 使用 ocr_parse_document_stream 工具获取实时进度反馈
3. 文件上传 - 支持 Base64 编码的文件内容上传，无需本地文件
4. 多格式支持 - 支持 PDF、图片等多种文档格式
5. 高质量输出 - 基于先进的视觉语言模型，提供高精度的文本识别

工具说明：
- ocr_parse_document: 直接解析文档，支持本地文件路径或上传文件内容
- ocr_parse_document_stream: 创建流式解析任务，支持进度跟踪
- cleanup_stream_task: 清理流式任务及其临时文件
- get_server_status: 获取服务器状态信息

文件处理方式：
1. 本地文件：提供 file_path 参数
2. 上传文件：提供 file_content (Base64编码) 和 filename 参数

使用时请确保：
1. VLLM 服务正在运行并可访问
2. 上传文件时使用正确的 Base64 编码
3. 根据需要调整解析参数（重试次数、跨页合并等）
4. 及时清理不需要的流式任务以释放临时文件

服务器连接到 VLLM 服务进行 OCR 处理，支持并发处理多个文档。
"""

# MCP 服务器实例
mcp = FastMCP(
    "OCRFlux Document Parser",
    instructions=OCRFLUX_MCP_INSTRUCTIONS,
)

# 流式任务存储
streaming_tasks: Dict[str, Dict[str, Any]] = {}

# 临时文件存储
temp_files: Dict[str, str] = {}


def save_uploaded_file(file_content: str, filename: str = None) -> str:
    """保存上传的文件到临时目录

    Args:
        file_content: Base64 编码的文件内容
        filename: 原始文件名（可选）

    Returns:
        临时文件路径
    """
    try:
        # 解码 Base64 内容
        file_data = base64.b64decode(file_content)

        # 生成临时文件
        file_id = str(uuid.uuid4())

        # 确定文件扩展名
        if filename:
            file_ext = Path(filename).suffix
        else:
            file_ext = ".tmp"

        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
            temp_file.write(file_data)
            temp_path = temp_file.name

        # 存储文件映射
        temp_files[file_id] = temp_path

        logger.info(f"保存上传文件: {filename} -> {temp_path} (ID: {file_id})")
        return temp_path

    except Exception as e:
        logger.error(f"保存上传文件失败: {e}")
        raise ValueError(f"文件保存失败: {e}")


def cleanup_temp_file(file_path: str):
    """清理临时文件"""
    try:
        if os.path.exists(file_path):
            os.unlink(file_path)
            logger.info(f"清理临时文件: {file_path}")

        # 从映射中移除
        for file_id, path in list(temp_files.items()):
            if path == file_path:
                del temp_files[file_id]
                break

    except Exception as e:
        logger.warning(f"清理临时文件失败: {e}")


@mcp.tool()
async def ocr_parse_document(
    file_path: Optional[str] = None,
    file_content: Optional[str] = None,
    filename: Optional[str] = None,
    skip_cross_page_merge: bool = False,
    max_page_retries: int = 1,
) -> OCRParseResponse | ErrorResponse:
    """解析文档并提取文本内容

    Args:
        file_path: 本地文档文件路径（与 file_content 二选一）
        file_content: Base64 编码的文件内容（与 file_path 二选一）
        filename: 原始文件名（当使用 file_content 时建议提供）
        skip_cross_page_merge: 是否跳过跨页合并，默认 False
        max_page_retries: 每页最大重试次数，默认 1

    Returns:
        解析结果或错误信息
    """
    global config

    temp_file_path = None

    try:
        # 确定要处理的文件路径
        if file_content:
            # 处理上传的文件内容
            if not file_content.strip():
                return ErrorResponse(error="文件内容不能为空")

            temp_file_path = save_uploaded_file(file_content, filename)
            actual_file_path = temp_file_path
            logger.info(f"处理上传文件: {filename} -> {actual_file_path}")

        elif file_path:
            # 处理本地文件路径
            if not os.path.exists(file_path):
                return ErrorResponse(error=f"文件不存在: {file_path}")
            actual_file_path = file_path
            logger.info(f"处理本地文件: {actual_file_path}")

        else:
            return ErrorResponse(error="必须提供 file_path 或 file_content 参数")

        logger.info(f"开始解析文档: {actual_file_path}")

        # 调用解析函数
        result = await parse_with_progress(
            config.vllm_url,
            config.model,
            actual_file_path,
            skip_cross_page_merge=skip_cross_page_merge,
            max_page_retries=max_page_retries,
        )

        if result is None:
            return ErrorResponse(error="文档解析失败")

        logger.info(f"文档解析完成: {actual_file_path}")

        return OCRParseResponse(message="文档解析成功", result=result)

    except Exception as e:
        error_msg = str(e)
        logger.error(f"解析文档时发生错误: {error_msg}")
        return ErrorResponse(error=f"解析失败: {error_msg}")

    finally:
        # 清理临时文件
        if temp_file_path:
            cleanup_temp_file(temp_file_path)


@mcp.tool()
async def ocr_parse_document_stream(
    file_path: Optional[str] = None,
    file_content: Optional[str] = None,
    filename: Optional[str] = None,
    skip_cross_page_merge: bool = False,
    max_page_retries: int = 1,
) -> StreamTaskResponse | ErrorResponse:
    """创建流式文档解析任务

    Args:
        file_path: 本地文档文件路径（与 file_content 二选一）
        file_content: Base64 编码的文件内容（与 file_path 二选一）
        filename: 原始文件名（当使用 file_content 时建议提供）
        skip_cross_page_merge: 是否跳过跨页合并，默认 False
        max_page_retries: 每页最大重试次数，默认 1

    Returns:
        流式任务信息或错误信息
    """
    global streaming_tasks

    temp_file_path = None

    try:
        # 确定要处理的文件路径
        if file_content:
            # 处理上传的文件内容
            if not file_content.strip():
                return ErrorResponse(error="文件内容不能为空")

            temp_file_path = save_uploaded_file(file_content, filename)
            actual_file_path = temp_file_path
            logger.info(f"处理上传文件用于流式任务: {filename} -> {actual_file_path}")

        elif file_path:
            # 处理本地文件路径
            if not os.path.exists(file_path):
                return ErrorResponse(error=f"文件不存在: {file_path}")
            actual_file_path = file_path
            logger.info(f"处理本地文件用于流式任务: {actual_file_path}")

        else:
            return ErrorResponse(error="必须提供 file_path 或 file_content 参数")

        # 生成任务 ID
        task_id = str(uuid.uuid4())

        # 存储任务参数
        streaming_tasks[task_id] = {
            "file_path": actual_file_path,
            "temp_file_path": temp_file_path,  # 记录临时文件路径用于后续清理
            "filename": filename,
            "skip_cross_page_merge": skip_cross_page_merge,
            "max_page_retries": max_page_retries,
            "created_at": datetime.now(timezone.utc).isoformat(),
        }

        logger.info(f"创建流式解析任务: {task_id} for {actual_file_path}")

        return StreamTaskResponse(
            message="流式解析任务创建成功",
            task_id=task_id,
            stream_url=f"/stream/{task_id}",
        )

    except Exception as e:
        error_msg = str(e)
        logger.error(f"创建流式任务时发生错误: {error_msg}")

        # 如果创建任务失败，立即清理临时文件
        if temp_file_path:
            cleanup_temp_file(temp_file_path)

        return ErrorResponse(error=f"创建任务失败: {error_msg}")


@mcp.tool()
async def cleanup_stream_task(task_id: str) -> SuccessResponse | ErrorResponse:
    """清理流式解析任务及其临时文件

    Args:
        task_id: 任务 ID

    Returns:
        清理结果
    """
    global streaming_tasks

    try:
        if task_id not in streaming_tasks:
            return ErrorResponse(error=f"任务不存在: {task_id}")

        task_info = streaming_tasks[task_id]
        temp_file_path = task_info.get("temp_file_path")

        # 清理临时文件
        if temp_file_path:
            cleanup_temp_file(temp_file_path)

        # 移除任务记录
        del streaming_tasks[task_id]

        logger.info(f"清理流式任务: {task_id}")

        return SuccessResponse(message=f"任务 {task_id} 清理成功")

    except Exception as e:
        error_msg = str(e)
        logger.error(f"清理任务时发生错误: {error_msg}")
        return ErrorResponse(error=f"清理失败: {error_msg}")


@mcp.tool()
async def get_server_status() -> Dict[str, Any]:
    """获取服务器状态信息

    Returns:
        服务器状态信息
    """
    global config

    return {
        "status": "running",
        "vllm_url": config.vllm_url,
        "model": config.model,
        "max_page_retries": config.max_page_retries,
        "skip_cross_page_merge": config.skip_cross_page_merge,
        "active_tasks": len(streaming_tasks),
        "temp_files": len(temp_files),
        "timestamp": datetime.now(timezone.utc).isoformat(),
    }


async def initialize_server() -> MCPConfig:
    """解析命令行参数并初始化服务器配置"""
    global config

    parser = argparse.ArgumentParser(description="运行 OCRFlux MCP 服务器")
    parser.add_argument(
        "--config", default="config.yaml", help="配置文件路径 (默认: config.yaml)"
    )
    parser.add_argument(
        "--transport",
        choices=["sse", "stdio"],
        help="传输协议 (覆盖配置文件)",
    )
    parser.add_argument("--vllm-url", help="VLLM 服务 URL (覆盖配置文件)")
    parser.add_argument("--model", help="模型名称 (覆盖配置文件)")
    parser.add_argument(
        "--max-page-retries", type=int, help="每页最大重试次数 (覆盖配置文件)"
    )
    parser.add_argument(
        "--skip-cross-page-merge",
        action="store_true",
        help="跳过跨页合并 (覆盖配置文件)",
    )
    parser.add_argument("--host", help="MCP 服务器监听地址 (覆盖配置文件)")
    parser.add_argument("--port", type=int, help="MCP 服务器监听端口 (覆盖配置文件)")

    args = parser.parse_args()

    # 加载配置文件
    config_data = load_config_file(args.config)

    # 构建配置
    config = OCRFluxConfig.from_config_file_and_cli(config_data, args)

    # 记录配置信息
    logger.info(f"配置文件: {args.config}")
    logger.info(f"VLLM URL: {config.vllm_url}")
    logger.info(f"模型: {config.model}")
    logger.info(f"最大重试次数: {config.max_page_retries}")
    logger.info(f"跳过跨页合并: {config.skip_cross_page_merge}")

    # 从配置文件或命令行获取 MCP 服务器参数
    mcp_config = config_data.get("mcp", {})
    fastapi_config = config_data.get("fastapi", {})

    # 确定传输协议
    transport = (
        args.transport
        or os.environ.get("MCP_TRANSPORT")
        or mcp_config.get("transport", "sse")
    )

    # 确定主机和端口
    host = (
        args.host
        or os.environ.get("MCP_SERVER_HOST")
        or fastapi_config.get("host", "localhost")
    )
    port = (
        args.port
        or int(os.environ.get("MCP_SERVER_PORT", "0"))
        or fastapi_config.get("port", 8080)
    )

    # 设置 MCP 服务器参数
    if host:
        logger.info(f"设置 MCP 服务器地址: {host}")
        mcp.settings.host = host

    if port:
        logger.info(f"设置 MCP 服务器端口: {port}")
        mcp.settings.port = int(port)

    # 创建 MCP 配置
    mcp_config_obj = MCPConfig(transport=transport)
    logger.info(f"传输协议: {transport}")

    return mcp_config_obj


async def run_mcp_server():
    """运行 MCP 服务器"""
    # 初始化服务器
    mcp_config = await initialize_server()

    # 运行服务器
    logger.info(f"启动 MCP 服务器，传输协议: {mcp_config.transport}")
    if mcp_config.transport == "stdio":
        await mcp.run_stdio_async()
    elif mcp_config.transport == "sse":
        logger.info(
            f"运行 MCP 服务器，SSE 传输协议，地址: {mcp.settings.host}:{mcp.settings.port}"
        )
        await mcp.run_sse_async()


def main():
    """主函数"""
    try:
        asyncio.run(run_mcp_server())
    except Exception as e:
        logger.error(f"启动 OCRFlux MCP 服务器时发生错误: {str(e)}")
        raise


if __name__ == "__main__":
    main()
