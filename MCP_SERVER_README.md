# OCRFlux MCP 服务器

基于 OCRFlux 的 MCP (Model Context Protocol) 服务器，支持 SSE (Server-Sent Events) 流式响应。

## 功能特性

- 🔧 **MCP 工具集成**: 将 OCRFlux OCR 功能封装为 MCP 工具
- 📡 **SSE 流式响应**: 提供实时进度反馈的流式 OCR 解析
- 🌐 **HTTP API**: FastAPI 驱动的 RESTful API
- ⚙️ **灵活配置**: 支持 YAML 配置文件和命令行参数
- 🔄 **多种运行模式**: MCP 服务器、HTTP API 服务器或同时运行

## 安装依赖

```bash
# 安装项目依赖
pip install -e .

# 或者手动安装依赖
pip install mcp fastapi uvicorn sse-starlette pyyaml
```

## 快速开始

### 1. 配置 VLLM 服务

确保您的 VLLM 服务正在运行，例如：

```bash
# 启动 VLLM 服务 (示例)
vllm serve OCRFlux-3B --host 0.0.0.0 --port 8000
```

### 2. 配置服务器

编辑 `config.yaml` 文件：

```yaml
vllm:
  url: "http://localhost:8000"
  model: "OCRFlux-3B"

fastapi:
  host: "0.0.0.0"
  port: 8080

ocr:
  max_page_retries: 3
  skip_cross_page_merge: false
```

### 3. 启动服务器

#### 方式一：使用启动脚本

**Windows:**
```cmd
start_server.bat
```

**Linux/macOS:**
```bash
./start_server.sh
```

#### 方式二：使用 Python 脚本

```bash
# 启动 FastAPI 服务器 (推荐)
python start_mcp_server.py --mode fastapi

# 启动 MCP 服务器
python start_mcp_server.py --mode mcp

# 同时启动两个服务器
python start_mcp_server.py --mode both
```

## 使用方法

### MCP 工具

服务器提供以下 MCP 工具：

#### 1. `ocr_parse_document`

解析文档并提取文本内容。

**参数:**
- `file_path` (string, 必需): 文档文件路径
- `skip_cross_page_merge` (boolean, 可选): 是否跳过跨页合并，默认 false
- `max_page_retries` (integer, 可选): 每页最大重试次数，默认 1

**示例:**
```json
{
  "name": "ocr_parse_document",
  "arguments": {
    "file_path": "/path/to/document.pdf",
    "skip_cross_page_merge": false,
    "max_page_retries": 3
  }
}
```

#### 2. `ocr_parse_document_stream`

创建流式解析任务，返回 SSE 流 URL。

**参数:** 与 `ocr_parse_document` 相同

### HTTP API

#### 1. 健康检查

```bash
GET /health
```

**响应:**
```json
{
  "status": "healthy",
  "vllm_url": "http://localhost:8000",
  "model": "OCRFlux-3B"
}
```

#### 2. 直接解析文档

```bash
POST /parse
Content-Type: application/json

{
  "file_path": "/path/to/document.pdf",
  "skip_cross_page_merge": false,
  "max_page_retries": 3
}
```

#### 3. 创建流式解析任务

```bash
POST /parse/stream
Content-Type: application/json

{
  "file_path": "/path/to/document.pdf",
  "skip_cross_page_merge": false,
  "max_page_retries": 3
}
```

**响应:**
```json
{
  "success": true,
  "task_id": "uuid-task-id",
  "stream_url": "/stream/uuid-task-id"
}
```

#### 4. 获取流式进度

```bash
GET /stream/{task_id}
Accept: text/event-stream
```

**SSE 事件类型:**
- `start`: 任务开始
- `progress`: 进度更新
- `complete`: 任务完成
- `error`: 错误信息

**示例事件:**
```
event: progress
data: {"progress": 0.5, "message": "正在解析第 2 页"}

event: complete
data: {"result": {...}, "message": "文档解析完成"}
```

## 配置选项

### 环境变量

- `VLLM_URL`: VLLM 服务 URL
- `MODEL_NAME`: 模型名称

### 命令行参数

```bash
python start_mcp_server.py --help
```

- `--mode`: 运行模式 (mcp, fastapi, both)
- `--config`: 配置文件路径
- `--vllm-url`: VLLM 服务 URL
- `--model`: 模型名称
- `--host`: FastAPI 服务器地址
- `--port`: FastAPI 服务器端口

## MCP 客户端配置

将以下配置添加到您的 MCP 客户端配置文件中：

```json
{
  "mcpServers": {
    "ocrflux": {
      "command": "python",
      "args": ["start_mcp_server.py", "--mode", "mcp"],
      "env": {
        "VLLM_URL": "http://localhost:8000",
        "MODEL_NAME": "OCRFlux-3B"
      }
    }
  }
}
```

## 故障排除

### 常见问题

1. **VLLM 连接失败**
   - 检查 VLLM 服务是否正在运行
   - 验证 URL 和端口配置

2. **文件不存在错误**
   - 确保文件路径正确
   - 检查文件权限

3. **依赖缺失**
   - 运行 `pip install -e .` 安装所有依赖

### 日志调试

设置日志级别为 DEBUG：

```yaml
logging:
  level: "DEBUG"
```

或使用环境变量：
```bash
export LOG_LEVEL=DEBUG
```

## 开发

### 项目结构

```
├── mcp_server.py           # MCP 服务器主文件
├── ocrflux/
│   ├── mcp_tools.py       # MCP 工具模块
│   └── inference.py       # OCR 推理模块
├── start_mcp_server.py    # 启动脚本
├── config.yaml            # 配置文件
└── MCP_SERVER_README.md   # 文档
```

### 扩展功能

要添加新的 MCP 工具：

1. 在 `OCRFluxMCPServer._setup_mcp_handlers()` 中添加工具定义
2. 实现对应的处理函数
3. 更新文档

## 许可证

本项目遵循与 OCRFlux 相同的许可证。
