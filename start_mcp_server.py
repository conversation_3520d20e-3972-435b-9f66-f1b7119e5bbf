#!/usr/bin/env python3
"""
OCRFlux MCP 服务器启动脚本
"""

import argparse
import asyncio
import logging
import os
import sys
import yaml
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from mcp_server import create_server

def load_config(config_path: str = "config.yaml") -> dict:
    """加载配置文件"""
    if not os.path.exists(config_path):
        print(f"配置文件不存在: {config_path}")
        print("使用默认配置...")
        return {}
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config or {}
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        print("使用默认配置...")
        return {}

def setup_logging(config: dict):
    """设置日志"""
    log_config = config.get('logging', {})
    level = getattr(logging, log_config.get('level', 'INFO').upper())
    format_str = log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    logging.basicConfig(level=level, format=format_str)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OCRFlux MCP 服务器")
    parser.add_argument(
        "--mode", 
        choices=["mcp", "fastapi", "both"], 
        default="fastapi",
        help="运行模式: mcp (MCP服务器), fastapi (HTTP API服务器), both (同时运行)"
    )
    parser.add_argument(
        "--config", 
        default="config.yaml",
        help="配置文件路径"
    )
    parser.add_argument(
        "--vllm-url",
        help="VLLM 服务 URL (覆盖配置文件)"
    )
    parser.add_argument(
        "--model",
        help="模型名称 (覆盖配置文件)"
    )
    parser.add_argument(
        "--host",
        help="FastAPI 服务器监听地址 (覆盖配置文件)"
    )
    parser.add_argument(
        "--port",
        type=int,
        help="FastAPI 服务器监听端口 (覆盖配置文件)"
    )
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    setup_logging(config)
    
    logger = logging.getLogger(__name__)
    logger.info("启动 OCRFlux MCP 服务器...")
    
    # 获取配置参数
    vllm_config = config.get('vllm', {})
    fastapi_config = config.get('fastapi', {})
    
    vllm_url = args.vllm_url or vllm_config.get('url', 'http://localhost:8000')
    model = args.model or vllm_config.get('model', 'OCRFlux-3B')
    host = args.host or fastapi_config.get('host', '0.0.0.0')
    port = args.port or fastapi_config.get('port', 8080)
    
    logger.info(f"VLLM URL: {vllm_url}")
    logger.info(f"模型: {model}")
    
    # 创建服务器实例
    server = create_server(vllm_url, model)
    
    if args.mode == "mcp":
        logger.info("启动 MCP 服务器 (stdio 模式)...")
        asyncio.run(server.run_mcp_server())
    elif args.mode == "fastapi":
        logger.info(f"启动 FastAPI 服务器 ({host}:{port})...")
        server.run_fastapi_server(host=host, port=port)
    elif args.mode == "both":
        logger.info("同时启动 MCP 和 FastAPI 服务器...")
        
        async def run_both():
            # 在后台启动 FastAPI 服务器
            import uvicorn
            from threading import Thread
            
            def run_fastapi():
                uvicorn.run(server.app, host=host, port=port)
            
            fastapi_thread = Thread(target=run_fastapi, daemon=True)
            fastapi_thread.start()
            
            logger.info(f"FastAPI 服务器已在后台启动 ({host}:{port})")
            logger.info("启动 MCP 服务器 (stdio 模式)...")
            
            # 运行 MCP 服务器
            await server.run_mcp_server()
        
        asyncio.run(run_both())

if __name__ == "__main__":
    main()
