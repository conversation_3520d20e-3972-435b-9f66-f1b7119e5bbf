<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCRFlux MCP 服务器 SSE 演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], input[type="url"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .progress-container {
            margin: 20px 0;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        
        .log-container {
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background-color: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        
        .log-start { background-color: #d4edda; color: #155724; }
        .log-progress { background-color: #d1ecf1; color: #0c5460; }
        .log-complete { background-color: #d4edda; color: #155724; }
        .log-error { background-color: #f8d7da; color: #721c24; }
        
        .result-container {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
            display: none;
        }
        
        .result-text {
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: white;
            padding: 10px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>OCRFlux MCP 服务器 SSE 演示</h1>
        
        <div class="form-group">
            <label for="serverUrl">服务器 URL:</label>
            <input type="url" id="serverUrl" value="http://localhost:8080" placeholder="http://localhost:8080">
        </div>
        
        <div class="form-group">
            <label for="filePath">文件路径:</label>
            <input type="text" id="filePath" placeholder="例如: C:\path\to\document.pdf">
        </div>
        
        <div class="form-group">
            <button onclick="startSSEParse()">开始 SSE 流式解析</button>
            <button onclick="startDirectParse()">直接 API 解析</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">准备中...</div>
        </div>
        
        <div class="log-container" id="logContainer"></div>
        
        <div class="result-container" id="resultContainer">
            <h3>解析结果:</h3>
            <div class="result-text" id="resultText"></div>
        </div>
    </div>

    <script>
        let eventSource = null;
        
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function updateProgress(progress, message) {
            const progressContainer = document.getElementById('progressContainer');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            progressContainer.style.display = 'block';
            progressFill.style.width = `${progress * 100}%`;
            progressText.textContent = `${(progress * 100).toFixed(1)}% - ${message}`;
        }
        
        function showResult(result) {
            const resultContainer = document.getElementById('resultContainer');
            const resultText = document.getElementById('resultText');
            
            resultContainer.style.display = 'block';
            
            const displayText = `文件: ${result.orig_path || ''}
页数: ${result.num_pages || 0}
失败页面: ${JSON.stringify(result.fallback_pages || [])}

文档内容:
${result.document_text || ''}`;
            
            resultText.textContent = displayText;
        }
        
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('resultContainer').style.display = 'none';
        }
        
        async function startSSEParse() {
            const serverUrl = document.getElementById('serverUrl').value;
            const filePath = document.getElementById('filePath').value;
            
            if (!filePath) {
                alert('请输入文件路径');
                return;
            }
            
            clearLog();
            log('开始 SSE 流式解析...', 'start');
            
            try {
                // 1. 创建流式任务
                const response = await fetch(`${serverUrl}/parse/stream`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        file_path: filePath,
                        skip_cross_page_merge: false,
                        max_page_retries: 2
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${await response.text()}`);
                }
                
                const result = await response.json();
                const taskId = result.task_id;
                const streamUrl = `${serverUrl}${result.stream_url}`;
                
                log(`任务创建成功，ID: ${taskId}`, 'start');
                log(`连接到 SSE 流: ${streamUrl}`, 'start');
                
                // 2. 连接 SSE 流
                eventSource = new EventSource(streamUrl);
                
                eventSource.onopen = function() {
                    log('SSE 连接已建立', 'start');
                };
                
                eventSource.addEventListener('start', function(event) {
                    const data = JSON.parse(event.data);
                    log(`开始: ${data.message}`, 'start');
                });
                
                eventSource.addEventListener('progress', function(event) {
                    const data = JSON.parse(event.data);
                    updateProgress(data.progress, data.message);
                    log(`进度: ${(data.progress * 100).toFixed(1)}% - ${data.message}`, 'progress');
                });
                
                eventSource.addEventListener('complete', function(event) {
                    const data = JSON.parse(event.data);
                    log(`完成: ${data.message}`, 'complete');
                    showResult(data.result);
                    eventSource.close();
                });
                
                eventSource.addEventListener('error', function(event) {
                    const data = JSON.parse(event.data);
                    log(`错误: ${data.error}`, 'error');
                    eventSource.close();
                });
                
                eventSource.onerror = function(event) {
                    log('SSE 连接错误', 'error');
                    eventSource.close();
                };
                
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
            }
        }
        
        async function startDirectParse() {
            const serverUrl = document.getElementById('serverUrl').value;
            const filePath = document.getElementById('filePath').value;
            
            if (!filePath) {
                alert('请输入文件路径');
                return;
            }
            
            clearLog();
            log('开始直接 API 解析...', 'start');
            updateProgress(0, '发送请求...');
            
            try {
                const response = await fetch(`${serverUrl}/parse`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        file_path: filePath,
                        skip_cross_page_merge: false,
                        max_page_retries: 1
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${await response.text()}`);
                }
                
                updateProgress(0.5, '等待响应...');
                
                const result = await response.json();
                
                if (result.success) {
                    updateProgress(1, '解析完成');
                    log('解析成功!', 'complete');
                    showResult(result.result);
                } else {
                    log('解析失败', 'error');
                }
                
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
            }
        }
        
        // 页面卸载时关闭 SSE 连接
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html>
