[project]
name = "ocrflux-main"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "beautifulsoup4>=4.13.4",
    "lxml>=6.0.0",
    "pillow>=11.3.0",
    "pymupdf>=1.26.3",
    "tqdm>=4.67.1",
    "mcp>=1.0.0",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "sse-starlette>=1.6.5",
    "pyyaml>=6.0",
    "python-dotenv>=1.0.0",
    "pydantic>=2.0.0",
]
