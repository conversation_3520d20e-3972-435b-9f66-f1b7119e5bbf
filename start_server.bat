@echo off
REM OCRFlux MCP 服务器启动脚本 (Windows)

echo 启动 OCRFlux MCP 服务器...

REM 检查 Python 是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到 Python，请先安装 Python 3.12+
    pause
    exit /b 1
)

REM 检查是否在虚拟环境中
if not defined VIRTUAL_ENV (
    echo 警告: 建议在虚拟环境中运行
)

REM 安装依赖（如果需要）
if not exist "venv" (
    echo 创建虚拟环境...
    python -m venv venv
    call venv\Scripts\activate.bat
    echo 安装依赖...
    pip install -e .
) else (
    call venv\Scripts\activate.bat
)

REM 检查配置文件
if not exist "config.yaml" (
    echo 警告: 未找到配置文件 config.yaml，将使用默认配置
)

REM 启动服务器
echo.
echo 选择启动模式:
echo 1. FastAPI 服务器 (HTTP API + SSE)
echo 2. MCP 服务器 (stdio)
echo 3. 同时启动两个服务器
echo.
set /p choice="请选择 (1-3): "

if "%choice%"=="1" (
    echo 启动 FastAPI 服务器...
    python start_mcp_server.py --mode fastapi
) else if "%choice%"=="2" (
    echo 启动 MCP 服务器...
    python start_mcp_server.py --mode mcp
) else if "%choice%"=="3" (
    echo 同时启动两个服务器...
    python start_mcp_server.py --mode both
) else (
    echo 无效选择，启动 FastAPI 服务器...
    python start_mcp_server.py --mode fastapi
)

pause
