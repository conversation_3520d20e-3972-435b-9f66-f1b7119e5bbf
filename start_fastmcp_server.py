#!/usr/bin/env python3
"""
OCRFlux FastMCP 服务器启动脚本
基于 FastMCP 框架的现代化 MCP 服务器
"""

import argparse
import asyncio
import logging
import os
import sys
import yaml
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ocrflux_mcp_server import run_mcp_server

def load_config(config_path: str = "config.yaml") -> dict:
    """加载配置文件"""
    if not os.path.exists(config_path):
        print(f"配置文件不存在: {config_path}")
        print("使用默认配置...")
        return {}
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config or {}
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        print("使用默认配置...")
        return {}

def setup_logging(config: dict):
    """设置日志"""
    log_config = config.get('logging', {})
    level = getattr(logging, log_config.get('level', 'INFO').upper())
    format_str = log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    logging.basicConfig(level=level, format=format_str)

def setup_environment_from_config(config: dict):
    """从配置文件设置环境变量"""
    vllm_config = config.get('vllm', {})
    mcp_config = config.get('mcp', {})
    ocr_config = config.get('ocr', {})
    
    # 设置环境变量（如果尚未设置）
    if 'VLLM_URL' not in os.environ and 'url' in vllm_config:
        os.environ['VLLM_URL'] = vllm_config['url']
    
    if 'MODEL_NAME' not in os.environ and 'model' in vllm_config:
        os.environ['MODEL_NAME'] = vllm_config['model']
    
    if 'MAX_PAGE_RETRIES' not in os.environ and 'max_page_retries' in ocr_config:
        os.environ['MAX_PAGE_RETRIES'] = str(ocr_config['max_page_retries'])
    
    if 'SKIP_CROSS_PAGE_MERGE' not in os.environ and 'skip_cross_page_merge' in ocr_config:
        os.environ['SKIP_CROSS_PAGE_MERGE'] = str(ocr_config['skip_cross_page_merge']).lower()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OCRFlux FastMCP 服务器")
    parser.add_argument(
        "--config", 
        default="config.yaml",
        help="配置文件路径"
    )
    parser.add_argument(
        "--transport",
        choices=["sse", "stdio"],
        default="sse",
        help="传输协议 (默认: sse)"
    )
    parser.add_argument(
        "--vllm-url",
        help="VLLM 服务 URL (覆盖配置文件)"
    )
    parser.add_argument(
        "--model",
        help="模型名称 (覆盖配置文件)"
    )
    parser.add_argument(
        "--max-page-retries",
        type=int,
        help="每页最大重试次数 (覆盖配置文件)"
    )
    parser.add_argument(
        "--skip-cross-page-merge",
        action="store_true",
        help="跳过跨页合并 (覆盖配置文件)"
    )
    parser.add_argument(
        "--host",
        help="MCP 服务器监听地址"
    )
    parser.add_argument(
        "--port",
        type=int,
        help="MCP 服务器监听端口"
    )
    
    args = parser.parse_args()
    
    # 加载配置文件
    config = load_config(args.config)
    setup_logging(config)
    setup_environment_from_config(config)
    
    logger = logging.getLogger(__name__)
    logger.info("启动 OCRFlux FastMCP 服务器...")
    
    # 构建命令行参数列表
    server_args = []
    
    # 传输协议
    server_args.extend(['--transport', args.transport])
    
    # VLLM 配置
    if args.vllm_url:
        server_args.extend(['--vllm-url', args.vllm_url])
    
    if args.model:
        server_args.extend(['--model', args.model])
    
    # OCR 配置
    if args.max_page_retries is not None:
        server_args.extend(['--max-page-retries', str(args.max_page_retries)])
    
    if args.skip_cross_page_merge:
        server_args.append('--skip-cross-page-merge')
    
    # 服务器配置
    if args.host:
        server_args.extend(['--host', args.host])
    
    if args.port is not None:
        server_args.extend(['--port', str(args.port)])
    
    # 设置命令行参数
    sys.argv = ['ocrflux_mcp_server.py'] + server_args
    
    logger.info(f"传输协议: {args.transport}")
    logger.info(f"VLLM URL: {os.environ.get('VLLM_URL', 'http://localhost:8000')}")
    logger.info(f"模型: {os.environ.get('MODEL_NAME', 'OCRFlux-3B')}")
    
    try:
        # 运行服务器
        asyncio.run(run_mcp_server())
    except KeyboardInterrupt:
        logger.info("服务器被用户中断")
    except Exception as e:
        logger.error(f"服务器运行失败: {e}")
        raise

if __name__ == "__main__":
    main()
