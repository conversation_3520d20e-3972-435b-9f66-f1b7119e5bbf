#!/usr/bin/env python3
"""
OCRFlux MCP 服务器 SSE 客户端测试
"""

import asyncio
import json
import sys
import aiohttp
from pathlib import Path

async def test_sse_stream(server_url: str, file_path: str):
    """测试 SSE 流式解析"""
    
    print(f"测试文件: {file_path}")
    print(f"服务器: {server_url}")
    print("-" * 50)
    
    async with aiohttp.ClientSession() as session:
        # 1. 创建流式任务
        print("1. 创建流式解析任务...")
        
        create_url = f"{server_url}/parse/stream"
        payload = {
            "file_path": file_path,
            "skip_cross_page_merge": False,
            "max_page_retries": 2
        }
        
        async with session.post(create_url, json=payload) as resp:
            if resp.status != 200:
                error_text = await resp.text()
                print(f"创建任务失败: {resp.status} - {error_text}")
                return
            
            result = await resp.json()
            task_id = result["task_id"]
            stream_url = f"{server_url}{result['stream_url']}"
            
            print(f"任务ID: {task_id}")
            print(f"流URL: {stream_url}")
        
        # 2. 连接 SSE 流
        print("\n2. 连接 SSE 流...")
        
        async with session.get(stream_url, headers={"Accept": "text/event-stream"}) as resp:
            if resp.status != 200:
                error_text = await resp.text()
                print(f"连接流失败: {resp.status} - {error_text}")
                return
            
            print("已连接到 SSE 流，等待事件...")
            print("-" * 30)
            
            async for line in resp.content:
                line = line.decode('utf-8').strip()
                
                if line.startswith('event:'):
                    event_type = line[6:].strip()
                elif line.startswith('data:'):
                    data_str = line[5:].strip()
                    
                    try:
                        data = json.loads(data_str)
                        
                        if event_type == "start":
                            print(f"🚀 开始: {data.get('message', '')}")
                            
                        elif event_type == "progress":
                            progress = data.get('progress', 0)
                            message = data.get('message', '')
                            progress_bar = "█" * int(progress * 20) + "░" * (20 - int(progress * 20))
                            print(f"📊 进度: [{progress_bar}] {progress:.1%} - {message}")
                            
                        elif event_type == "complete":
                            print(f"✅ 完成: {data.get('message', '')}")
                            result = data.get('result', {})
                            
                            print(f"\n📄 解析结果:")
                            print(f"   文件: {result.get('orig_path', '')}")
                            print(f"   页数: {result.get('num_pages', 0)}")
                            print(f"   失败页面: {result.get('fallback_pages', [])}")
                            
                            # 显示部分文本内容
                            document_text = result.get('document_text', '')
                            if document_text:
                                preview = document_text[:200] + "..." if len(document_text) > 200 else document_text
                                print(f"   内容预览: {preview}")
                            
                            break
                            
                        elif event_type == "error":
                            print(f"❌ 错误: {data.get('error', '')}")
                            break
                            
                    except json.JSONDecodeError:
                        print(f"无法解析数据: {data_str}")

async def test_direct_api(server_url: str, file_path: str):
    """测试直接 API 调用"""
    
    print(f"\n测试直接 API 调用...")
    print("-" * 30)
    
    async with aiohttp.ClientSession() as session:
        url = f"{server_url}/parse"
        payload = {
            "file_path": file_path,
            "skip_cross_page_merge": False,
            "max_page_retries": 1
        }
        
        print("发送解析请求...")
        
        async with session.post(url, json=payload) as resp:
            if resp.status != 200:
                error_text = await resp.text()
                print(f"请求失败: {resp.status} - {error_text}")
                return
            
            result = await resp.json()
            
            if result.get("success"):
                data = result["result"]
                print(f"✅ 解析成功!")
                print(f"   文件: {data.get('orig_path', '')}")
                print(f"   页数: {data.get('num_pages', 0)}")
                print(f"   失败页面: {data.get('fallback_pages', [])}")
                
                # 显示部分文本内容
                document_text = data.get('document_text', '')
                if document_text:
                    preview = document_text[:200] + "..." if len(document_text) > 200 else document_text
                    print(f"   内容预览: {preview}")
            else:
                print(f"❌ 解析失败")

async def check_server_health(server_url: str):
    """检查服务器健康状态"""
    
    print(f"检查服务器健康状态...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{server_url}/health") as resp:
                if resp.status == 200:
                    health = await resp.json()
                    print(f"✅ 服务器健康")
                    print(f"   VLLM URL: {health.get('vllm_url', '')}")
                    print(f"   模型: {health.get('model', '')}")
                    return True
                else:
                    print(f"❌ 服务器不健康: {resp.status}")
                    return False
        except Exception as e:
            print(f"❌ 无法连接到服务器: {e}")
            return False

async def main():
    """主函数"""
    
    if len(sys.argv) < 2:
        print("用法: python test_sse_client.py <文件路径> [服务器URL]")
        print("示例: python test_sse_client.py test.pdf http://localhost:8080")
        sys.exit(1)
    
    file_path = sys.argv[1]
    server_url = sys.argv[2] if len(sys.argv) > 2 else "http://localhost:8080"
    
    # 检查文件是否存在
    if not Path(file_path).exists():
        print(f"错误: 文件不存在 - {file_path}")
        sys.exit(1)
    
    print("OCRFlux MCP 服务器 SSE 客户端测试")
    print("=" * 50)
    
    # 检查服务器健康状态
    if not await check_server_health(server_url):
        print("请确保服务器正在运行并且可访问")
        sys.exit(1)
    
    print("\n选择测试模式:")
    print("1. SSE 流式解析 (推荐)")
    print("2. 直接 API 调用")
    print("3. 两种模式都测试")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
    except KeyboardInterrupt:
        print("\n测试取消")
        sys.exit(0)
    
    try:
        if choice == "1":
            await test_sse_stream(server_url, file_path)
        elif choice == "2":
            await test_direct_api(server_url, file_path)
        elif choice == "3":
            await test_sse_stream(server_url, file_path)
            await test_direct_api(server_url, file_path)
        else:
            print("无效选择，使用 SSE 流式解析")
            await test_sse_stream(server_url, file_path)
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
