{"mcpServers": {"ocrflux": {"command": "python", "args": ["start_fastmcp_server.py", "--transport", "stdio"], "env": {"VLLM_URL": "http://localhost:8000", "MODEL_NAME": "OCRFlux-3B", "MAX_PAGE_RETRIES": "3", "SKIP_CROSS_PAGE_MERGE": "false"}}, "ocrflux-sse": {"command": "python", "args": ["start_fastmcp_server.py", "--transport", "sse", "--host", "localhost", "--port", "8080"], "env": {"VLLM_URL": "http://localhost:8000", "MODEL_NAME": "OCRFlux-3B", "MCP_SERVER_HOST": "localhost", "MCP_SERVER_PORT": "8080"}}}}