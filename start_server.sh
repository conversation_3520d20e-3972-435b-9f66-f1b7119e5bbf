#!/bin/bash
# OCRFlux MCP 服务器启动脚本 (Linux/macOS)

echo "启动 OCRFlux MCP 服务器..."

# 检查 Python 是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到 Python3，请先安装 Python 3.12+"
    exit 1
fi

# 检查是否在虚拟环境中
if [[ -z "$VIRTUAL_ENV" ]]; then
    echo "警告: 建议在虚拟环境中运行"
fi

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
    source venv/bin/activate
    echo "安装依赖..."
    pip install -e .
else
    source venv/bin/activate
fi

# 检查配置文件
if [ ! -f "config.yaml" ]; then
    echo "警告: 未找到配置文件 config.yaml，将使用默认配置"
fi

# 启动服务器
echo ""
echo "选择启动模式:"
echo "1. FastMCP 服务器 (SSE 传输)"
echo "2. FastMCP 服务器 (stdio 传输)"
echo "3. 传统 FastAPI 服务器 (HTTP API + SSE)"
echo "4. 传统 MCP 服务器 (stdio)"
echo ""
read -p "请选择 (1-4): " choice

case $choice in
    1)
        echo "启动 FastMCP 服务器 (SSE 传输)..."
        python start_fastmcp_server.py --transport sse
        ;;
    2)
        echo "启动 FastMCP 服务器 (stdio 传输)..."
        python start_fastmcp_server.py --transport stdio
        ;;
    3)
        echo "启动传统 FastAPI 服务器..."
        python start_mcp_server.py --mode fastapi
        ;;
    4)
        echo "启动传统 MCP 服务器..."
        python start_mcp_server.py --mode mcp
        ;;
    *)
        echo "无效选择，启动 FastMCP 服务器 (SSE 传输)..."
        python start_fastmcp_server.py --transport sse
        ;;
esac
