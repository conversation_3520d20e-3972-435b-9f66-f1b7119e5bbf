#!/bin/bash
# OCRFlux MCP 服务器启动脚本 (Linux/macOS)

echo "启动 OCRFlux MCP 服务器..."

# 检查 Python 是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到 Python3，请先安装 Python 3.12+"
    exit 1
fi

# 检查是否在虚拟环境中
if [[ -z "$VIRTUAL_ENV" ]]; then
    echo "警告: 建议在虚拟环境中运行"
fi

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
    source venv/bin/activate
    echo "安装依赖..."
    pip install -e .
else
    source venv/bin/activate
fi

# 检查配置文件
if [ ! -f "config.yaml" ]; then
    echo "警告: 未找到配置文件 config.yaml，将使用默认配置"
fi

# 启动服务器
echo ""
echo "选择启动模式:"
echo "1. FastAPI 服务器 (HTTP API + SSE)"
echo "2. MCP 服务器 (stdio)"
echo "3. 同时启动两个服务器"
echo ""
read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo "启动 FastAPI 服务器..."
        python start_mcp_server.py --mode fastapi
        ;;
    2)
        echo "启动 MCP 服务器..."
        python start_mcp_server.py --mode mcp
        ;;
    3)
        echo "同时启动两个服务器..."
        python start_mcp_server.py --mode both
        ;;
    *)
        echo "无效选择，启动 FastAPI 服务器..."
        python start_mcp_server.py --mode fastapi
        ;;
esac
